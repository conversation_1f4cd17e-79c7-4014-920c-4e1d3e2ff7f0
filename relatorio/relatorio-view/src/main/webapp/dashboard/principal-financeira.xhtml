<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">


    <ui:define name="content">

        <div class="row">
            <div class="col-sm-12">
                <h4 class="header-title m-t-0 m-b-20">Seja Bem-vindo!</h4>
            </div>
            <div class="col-sm-12">
                <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                            infoClass="alert alert-success alert-dismissable"
                            errorClass="alert alert-danger alert-dismissable"/>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="card-box">
                    <h6 class="m-t-0">Contratos Registrados</h6>

                </div>
            </div>
            <div jsf:id="lineChartTransacao">
                <p:lineChart style="width: 100%; height: 100vh;"
                             rendered="#{dashboardBean.lineChartModelTransacoes ne null}"
                             model="#{dashboardBean.lineChartModelTransacoes}"/>
            </div>
        </div>
    </ui:define>

</ui:composition>
