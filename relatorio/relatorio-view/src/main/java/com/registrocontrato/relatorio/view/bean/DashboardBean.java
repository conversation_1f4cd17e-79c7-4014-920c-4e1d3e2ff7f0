package com.registrocontrato.relatorio.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.relatorio.service.RelatorioService;
import com.registrocontrato.relatorio.service.dto.DashboardDTO;
import org.primefaces.model.charts.line.LineChartModel;
import org.springframework.stereotype.Controller;

import javax.annotation.PostConstruct;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@ViewScope
@Controller
public class DashboardBean extends BaseBean {

    private LineChartModel lineChartModel;

    private final RelatorioService relatorioService;

    public DashboardBean(RelatorioService relatorioService) {
        this.relatorioService = relatorioService;
    }

    @PostConstruct
    public void init() {
        lineChartModel = new LineChartModel();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 2020);
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // Janeiro é 0
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date data = calendar.getTime();
        buildChart(relatorioService.findDashboard(data, getUsername()));
    }

    private void buildChart(List<DashboardDTO> list) {

    }

    public LineChartModel getLineChartModel() {
        return lineChartModel;
    }
}
