package com.registrocontrato.relatorio.service.dto;

import com.registrocontrato.infra.entity.Uf;

import java.math.BigDecimal;

public class DashboardDTO {
    Uf uf;
    Long quantidadeContratos;
    BigDecimal valorCredenciada;
    BigDecimal valorDetran;
    BigDecimal percentualDesconto;
    BigDecimal valorFinal;
    String financeira;

    public DashboardDTO(Uf uf, Long quantidadeContratos, BigDecimal valorCredenciada, BigDecimal valorDetran, BigDecimal percentualDesconto, BigDecimal valorFinal, String financeira) {
        this.uf = uf;
        this.quantidadeContratos = quantidadeContratos;
        this.valorCredenciada = valorCredenciada;
        this.valorDetran = valorDetran;
        this.percentualDesconto = percentualDesconto;
        this.valorFinal = valorFinal;
        this.financeira = financeira;
    }

    public String getFinanceira() {
        return financeira;
    }

    public void setFinanceira(String financeira) {
        this.financeira = financeira;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public Long getQuantidadeContratos() {
        return quantidadeContratos;
    }

    public void setQuantidadeContratos(Long quantidadeContratos) {
        this.quantidadeContratos = quantidadeContratos;
    }

    public BigDecimal getValorCredenciada() {
        return valorCredenciada;
    }

    public void setValorCredenciada(BigDecimal valorCredenciada) {
        this.valorCredenciada = valorCredenciada;
    }

    public BigDecimal getValorDetran() {
        return valorDetran;
    }

    public void setValorDetran(BigDecimal valorDetran) {
        this.valorDetran = valorDetran;
    }

    public BigDecimal getPercentualDesconto() {
        return percentualDesconto;
    }

    public void setPercentualDesconto(BigDecimal percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public BigDecimal getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(BigDecimal valorFinal) {
        this.valorFinal = valorFinal;
    }
}
