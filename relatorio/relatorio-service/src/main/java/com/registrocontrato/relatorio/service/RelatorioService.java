package com.registrocontrato.relatorio.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.SituacaoFinanceira;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.relatorio.repository.RelatorioRepository;
import com.registrocontrato.relatorio.service.dto.*;
import com.registrocontrato.seguranca.entity.Perfil;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.*;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class RelatorioService extends BaseService<Contrato, RelatorioDTO> {

    private static final int MAX_REGISTRO = 200000;

    private static final long serialVersionUID = 1L;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private RelatorioRepository relatorioRepository;

    public List<PrecoComposto> findPrecosCompostos(Credenciamento credenciamento) {
        return relatorioRepository.findPrecosCompostos(credenciamento);
    }

    @Override
    public Page<Contrato> findAll(int first, int pageSize, RelatorioDTO filter) {
        Specification<Contrato> contratoSpec = new Specification<Contrato>() {

            @Override
            public Predicate toPredicate(Root<Contrato> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {

                cq.distinct(true);
                if (Long.class != cq.getResultType()) {
                    Fetch<Contrato, Veiculo> joinVeiculos = root.fetch("veiculos", JoinType.LEFT);
                    joinVeiculos.fetch("marca", JoinType.LEFT);
                    joinVeiculos.fetch("modelo", JoinType.LEFT);
                }
                if (pageSize == 0 && Long.class != cq.getResultType()) {
                    root.fetch("cobranca", JoinType.LEFT);
                }

                List<Predicate> predicates = new ArrayList<>();

                Date dataInicio = DateUtils.truncate(filter.getDataInicio(), Calendar.DATE);
                Calendar dataFim = DateUtils.toCalendar(filter.getDataFim());
                dataFim.set(Calendar.HOUR_OF_DAY, dataFim.getActualMaximum(Calendar.HOUR_OF_DAY));
                dataFim.set(Calendar.MINUTE, dataFim.getActualMaximum(Calendar.MINUTE));
                dataFim.set(Calendar.MILLISECOND, dataFim.getActualMaximum(Calendar.MILLISECOND));

                Predicate dataConclusao = cb.between(root.<Date>get("dataConclusaoDETRAN"), dataInicio, dataFim.getTime());

                Predicate dataCadastro = cb.between(root.<Date>get("dataCadastro"), dataInicio, dataFim.getTime());
                Predicate situacaoCadastro = cb.isNull(root.<Long>get("dataConclusaoDETRAN"));

                predicates.add(cb.or(dataConclusao, cb.and(dataCadastro, situacaoCadastro)));

                if (filter.getSituacao() != null) {
                    predicates.add(cb.equal(root.<Situacao>get("situacao"), filter.getSituacao()));
                }
                if (filter.getSituacaoFinanceira() != null) {
                    predicates.add(cb.equal(root.<SituacaoFinanceira>get("situacaoFinanceira"), filter.getSituacaoFinanceira()));
                }
                if (filter.getTipoContrato() != null) {
                    predicates.add(cb.equal(root.<TipoContrato>get("tipoContrato"), filter.getTipoContrato()));
                }
                if (filter.getTipoRestricao() != null) {
                    predicates.add(cb.equal(root.<TipoRestricao>get("tipoRestricao"), filter.getTipoRestricao()));
                }
                if (filter.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), filter.getUf()));
                }
                if (filter.getFinanceira() != null) {
                    predicates.add(cb.equal(root.<Financeira>get("financeira"), filter.getFinanceira()));
                }
                if (filter.getMensagemRetorno() != null) {
                    predicates.add(cb.equal(root.join("veiculos").<Long>get("mensagemRetorno"), filter.getMensagemRetorno()));
                }
                if (filter.getAnexo() != null) {
                    Join<Contrato, Anexo> joinAnexo = root.join("anexos", JoinType.LEFT);
                    if (filter.getAnexo() == SimNao.N) {
                        predicates.add(cb.isNull(joinAnexo));
                    } else {
                        predicates.add(cb.isNotNull(joinAnexo));
                    }
                }

                // se o usuario eh financeira filtrar somente os usuario da financeira
                Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
                if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA) {
                    List<Financeira> values = usuarioLogado.getFinanceiras();
                    predicates.add(root.<Financeira>get("financeira").in(values));
                }
                if (usuarioLogado.getUf() != null) {
                    predicates.add(cb.equal(root.<Uf>get("ufRegistro"), usuarioLogado.getUf()));
                }

                if (filter.getDataConclusaoDetran() != null) {
                    LocalDate lInicio = new Date(filter.getDataConclusaoDetran().getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate lFim = lInicio.plusDays(1);
                    predicates.add(cb.between(root.<Date>get("dataConclusaoDETRAN"), java.sql.Date.valueOf(lInicio), java.sql.Date.valueOf(lFim)));
                }

                return andTogether(predicates, cb);
            }

            private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
                return cb.and(predicates.toArray(new Predicate[0]));
            }
        };

        if (pageSize == 0) {
            Page<Contrato> list = relatorioRepository.findAll(contratoSpec, new PageRequest(0, MAX_REGISTRO, new Sort(Direction.ASC, "id")));
            return list;
        }

        return relatorioRepository.findAll(contratoSpec, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "id")));
    }


    public List<Contrato> findConcluidosDetran(Date data, Uf uf) {
        LocalDate lInicio = new Date(data.getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().toLocalDate();
        LocalDate lFim = lInicio.plusDays(1);
        return relatorioRepository.findByDataConclusaoDetran(java.sql.Date.valueOf(lInicio), java.sql.Date.valueOf(lFim), uf);
    }

    public Credenciamento findByUfAndPeriodo(Uf uf, Date dataInicio, Date dataFim) {
        return relatorioRepository.findByUfAndPeriodo(uf, dataInicio, dataFim);
    }

    public List<ResultadoGrupoDTO> findByMesAnoGrupo(RelatorioDTO filter) {
        List<Financeira> financeiras = null;
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA) {
            financeiras = usuarioLogado.getFinanceiras();
        }

        if (filter.getFinanceira() != null) {
            if (financeiras == null) {
                financeiras = new ArrayList<>();
                financeiras.add(filter.getFinanceira());
            } else {
                financeiras.retainAll(Arrays.asList(filter.getFinanceira()));
            }
        }

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        Boolean integraB3 = null;

        if (filter.getGrupo().equals("S")) {
            if (financeiras != null) {
                return relatorioRepository.countBySituacao(financeiras, usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
            }
            return relatorioRepository.countBySituacao(usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("R")) {
            if (financeiras != null) {
                return relatorioRepository.countByRestricao(financeiras, usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
            }
            return relatorioRepository.countByRestricao(usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("U")) {
            if (financeiras != null) {
                return relatorioRepository.countByUf(financeiras, usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
            }
            return relatorioRepository.countByUf(usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
        } else if (filter.getGrupo().equals("T")) {
            if (financeiras != null) {
                return relatorioRepository.countByTipoContrato(financeiras, usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
            }
            return relatorioRepository.countByTipoContrato(usuarioLogado.getUf(), dataInicio, dataFim, integraB3);
        }
        return Collections.emptyList();
    }

    public List<ResultadoDiarioDTO> findByDia(RelatorioDTO filter) {
        List<Financeira> financeiras = null;
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA) {
            financeiras = usuarioLogado.getFinanceiras();
        }

        if (filter.getFinanceira() != null) {
            if (financeiras == null) {
                financeiras = new ArrayList<>();
                financeiras.add(filter.getFinanceira());
            } else {
                financeiras.retainAll(Arrays.asList(filter.getFinanceira()));
            }
        }

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        if (financeiras != null) {
            return relatorioRepository.countByDia(financeiras, usuarioLogado.getUf(), filter.getSituacao(), filter.getTipoContrato(), filter.getTipoRestricao(), dataInicio, dataFim);
        }

        return relatorioRepository.countByDia(usuarioLogado.getUf(), filter.getSituacao(), filter.getTipoContrato(), filter.getTipoRestricao(), dataInicio, dataFim);
    }

    private Calendar setDataFim(RelatorioDTO filter) {
        Calendar dataFim = Calendar.getInstance();
        dataFim.set(Calendar.MONTH, filter.getMes() - 1);
        dataFim.set(Calendar.YEAR, filter.getAno());
        dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataFim.set(Calendar.HOUR_OF_DAY, dataFim.getActualMaximum(Calendar.HOUR_OF_DAY));
        dataFim.set(Calendar.MINUTE, dataFim.getActualMaximum(Calendar.MINUTE));
        dataFim.set(Calendar.MILLISECOND, dataFim.getActualMaximum(Calendar.MILLISECOND));
        return dataFim;
    }

    private Calendar setDataInicio(RelatorioDTO filter) {
        Calendar dataInicio = Calendar.getInstance();
        dataInicio.set(Calendar.DAY_OF_MONTH, 1);
        dataInicio.set(Calendar.MONTH, filter.getMes() - 1);
        dataInicio.set(Calendar.YEAR, filter.getAno());
        dataInicio = DateUtils.truncate(dataInicio, Calendar.DATE);
        return dataInicio;
    }

    public List<ResultadoDiarioUsuarioDTO> findByDiaUsuario(RelatorioDTO filter) {
        List<Long> financeiras = new ArrayList<>();
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(filter.getUsuario());
        if (usuarioLogado.getPerfil() == Perfil.FINANCEIRA) {
            financeiras = usuarioLogado.getFinanceiras().stream().map(Financeira::getId).collect(Collectors.toList());
        }
        financeiras.add(0L);

        Date dataInicio = setDataInicio(filter).getTime();
        Date dataFim = setDataFim(filter).getTime();

        List<Object[]> list = relatorioRepository.findByDiaUsuario(financeiras, financeiras.size(), usuarioLogado.getUf() == null ? "" : usuarioLogado.getUf().toString(), dataInicio, dataFim);
        List<ResultadoDiarioUsuarioDTO> retorno = new ArrayList<>();
        for (Object[] l : list) {
            retorno.add(new ResultadoDiarioUsuarioDTO(Uf.valueOf((String) l[0]), (String) l[1], (String) l[2], (Date) l[3], (BigInteger) l[4], (BigInteger) l[5], (BigInteger) l[6]));
        }

        return retorno;
    }

    public List<DashboardDTO> findDashboard(Date data, String usuario) {
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(usuario);
        List<Financeira> financeiras = usuarioLogado.getFinanceiras();
        return relatorioRepository.findDashboard(data, financeiras);
    }

    @Override
    protected PagingAndSortingRepository<Contrato, Long> getRepository() {
        return relatorioRepository;
    }

}
