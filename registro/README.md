Registro Contrato - Sistema de Registro de Contrato
============================

# Requisitos
* JDK 1.8+
* JBOSS EAP 7
* Configurar HTTPS
* Configurar Driver e DataSources no servidor de aplicações
* Configurar o serviço de autenticação central


# Configuracao
Atualize os valores do arquivo registro/registro-view/src/main/resources/application-prod.properties com os valores correspondentes ao seu ambiente:

```
#datasource
spring.datasource.jndi-name=java:jboss/datasources/RegistrDS

#cas
cas.server=https://registrocontrato.com/auth # nome do servico de autenticacao
cas.local.login=https://registrocontrato.com/registro/login/cas # nome do servico criado para esse modulo
```

# Deployment
Execute os seguintes comandos com apoio do maven

```
mvn clean install -Pprod
cd registro/regitro-view/
mvn wildfly:deploy -Dwildfly.hostname=IP_DO_SHOST -Dwildfly.username=NOME_DO_USUARIO_DE_DEPLOY -Dwildfly.password=SENHA_DO_USUARIO -Pprod
```

# Credenciais inicias
usuario: admin
senha: admin
