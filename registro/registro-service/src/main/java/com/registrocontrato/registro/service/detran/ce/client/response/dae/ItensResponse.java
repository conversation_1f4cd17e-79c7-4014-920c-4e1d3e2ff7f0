package com.registrocontrato.registro.service.detran.ce.client.response.dae;

import java.math.BigInteger;
import java.util.Date;

public class ItensResponse {

    private String chassi;
    private String cnpjEntidade;
    private Date dataCriacao;
    private String numeroContrato;
    private BigInteger numeroDae;
    private Integer numeroRestricaoGravame;
    private String place;
    private String servicoDescricao;
    private String status;
    private Integer statusId;

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getCnpjEntidade() {
        return cnpjEntidade;
    }

    public void setCnpjEntidade(String cnpjEntidade) {
        this.cnpjEntidade = cnpjEntidade;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public BigInteger getNumeroDae() {
        return numeroDae;
    }

    public void setNumeroDae(BigInteger numeroDae) {
        this.numeroDae = numeroDae;
    }

    public Integer getNumeroRestricaoGravame() {
        return numeroRestricaoGravame;
    }

    public void setNumeroRestricaoGravame(Integer numeroRestricaoGravame) {
        this.numeroRestricaoGravame = numeroRestricaoGravame;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getServicoDescricao() {
        return servicoDescricao;
    }

    public void setServicoDescricao(String servicoDescricao) {
        this.servicoDescricao = servicoDescricao;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getStatusId() {
        return statusId;
    }

    public void setStatusId(Integer statusId) {
        this.statusId = statusId;
    }
}
