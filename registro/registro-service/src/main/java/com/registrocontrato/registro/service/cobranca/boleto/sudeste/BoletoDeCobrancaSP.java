package com.registrocontrato.registro.service.cobranca.boleto.sudeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.registro.service.cobranca.boleto.CobrancaComBoletoDeReembolso;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class BoletoDeCobrancaSP extends CobrancaComBoletoDeReembolso {

    public BoletoDeCobrancaSP(FinanceiraService financeiraService, BoletoService boletoService) {
        super(financeiraService, boletoService);
    }

    @Override
    public void emitirBoleto(Cobranca cobranca) throws Exception {
        if (isCobrancaOfDesenvolveSp(cobranca)){
            cobranca.setDataVencimentoBoleto(getDataPlusDays(32));
            cobranca.setDataVencimentoReembolsoBoleto(getDataPlusDays(32));
        }
        super.emitirBoleto(cobranca);
    }

    //TODO: colocar no PlaceconUtil
    private Date getDataPlusDays(int days) {
        LocalDate dateTarget = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(days);
        return converteLocalDateEmDate(dateTarget);
    }

    //TODO: usar o do PlaceconUtil
    private Date converteLocalDateEmDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    private Boolean isCobrancaOfDesenvolveSp(Cobranca entity) {
        return entity.getFinanceira().getDocumento().equals("10663610000129");
    }

    @Override
    public Uf getUf() {
        return Uf.SP;
    }

    @Override
    public List<String> getCnpjFinanceiras() {
        return Collections.emptyList();
    }
}
