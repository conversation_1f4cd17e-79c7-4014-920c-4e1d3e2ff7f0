<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">

        <f:metadata>
            <f:viewParam id="id" name="id" value="#{cobrancaBean.idToEdit}"/>
            <f:viewAction action="#{cobrancaBean.loadDetails()}"/>
        </f:metadata>

        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Cobrança</h4>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Visualizar</h6>
                                    <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>
                                    <form jsf:id="form" jsf:prependId="false">
                                        <ui:include src="#{cobrancaBean.entity.isUnificada() ? 'form-inputs-unificada.xhtml' : 'form-inputs.xhtml'}">
                                            <ui:param name="disabled" value="disabled"></ui:param>
                                        </ui:include>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/cobranca/list.xhtml"
                                                   class="btn btn-default">Voltar</a>
                                                <sec:authorize ifAnyGranted="CADASTRAR_COBRANCA">
                                                    <a class="btn btn-primary btn-cons" title="Editar"
                                                       href="#{request.contextPath}/cobranca/form-update.xhtml?id=#{cobrancaBean.entity.id}">
                                                        Editar
                                                    </a>
                                                </sec:authorize>
                                                <sec:authorize ifAnyGranted="EXCLUIR_COBRANCA">
                                                    <h:commandLink styleClass="btn btn-primary btn-cons" title="Excluir"
                                                                   immediate="true"
                                                                   rendered="#{cobrancaBean.entity.situacaoCobranca == 'GERADA'}"
                                                                   onclick="return confirm('Confirma a exclusão?')"
                                                                   action="#{cobrancaBean.delete(cobrancaBean.entity.id)}">
                                                        Excluir
                                                    </h:commandLink>
                                                </sec:authorize>

                                                <a href="#" jsf:immediate="true" jsf:action="#{cobrancaBean.excel(cobrancaBean.entity)}"
                                                   class="btn btn-primary btn-cons">Exportar Relatório</a>

                                                <p:commandLink rendered="#{cobrancaBean.entity.notaFiscal ne null}"
                                                               actionListener="#{cobrancaBean.download(cobrancaBean.entity)}"
                                                               styleClass="btn btn-primary btn-cons"
                                                               title="Download da Nota Fiscal" ajax="false"
                                                               onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
                                                    Download nota fiscal
                                                    <p:fileDownload value="#{cobrancaBean.fileNf}"/>
                                                </p:commandLink>

                                                <p:commandLink rendered="#{cobrancaBean.entity.boletoEmitido}"
                                                               actionListener="#{cobrancaBean.download(cobrancaBean.entity)}"
                                                               styleClass="btn btn-primary btn-cons"
                                                               title="Download do Boleto" ajax="false"
                                                               onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
                                                    Download boleto
                                                    <p:fileDownload value="#{cobrancaBean.entity.file}"/>
                                                </p:commandLink>

                                                <input type="hidden" name="${_csrf.parameterName}"
                                                       value="${_csrf.token}"/>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
