<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:sec="http://www.springframework.org/security/tags"
	xmlns:jsf="http://xmlns.jcp.org/jsf">
	
	<div class="panel panel-default">
		<div class="panel-heading">
			<div class="panel-title">Documentos</div>
		</div>
		<div class="panel-body" jsf:id="documentos">
			<div class="row">
				<div class="col-lg-12">
					<p:fileUpload listener="#{financeiraBean.handleFileUpload}" mode="advanced"
								  dragDropSupport="true"
								  update="documentos" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
								  rendered="#{disabled != 'disabled'}"
								  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Arquivo" fileLimit="5"
								  multiple="true"
								  fileLimitMessage="Selecione no máximo 5 arquivos."
								  invalidFileMessage="Envie um arquivo em formato PDF"
								  invalidSizeMessage="Tamanho máximo 10M"/>
				</div>
			</div>	
			<div class="row">
				<div class="col-lg-12">
					<span>
						<p:dataTable id="dataTableAnexo" var="object" value="#{financeiraBean.entity.anexos}" rowIndexVar="index"
							tableStyleClass="table table-hover m-0" rendered="#{not empty financeiraBean.entity.anexos}">
							<p:column headerText="Nome do Arquivo">
								#{object.nomeArquivo}
							</p:column>
							<p:column headerText="Ações" styleClass="text-center">
								<sec:authorize ifAnyGranted="EXCLUIR_DOCUMENTO">
									<h:commandLink styleClass="btn btn-link" title="Excluir Arquivo" rendered="#{disabled != 'disabled'  or anexar}"
										onclick="return confirm('Confirmar a exclusão do arquivo?')" action="#{financeiraBean.deleteFile(index)}">
										<span class="fa fa-remove" />
										<f:ajax render="documentos" execute="@this"/>
									</h:commandLink>
								</sec:authorize>
								<h:commandLink styleClass="btn btn-link" title="Download" rendered="#{object.id != null}"
									target="_blank" action="#{financeiraBean.download(object.id)}" immediate="true">
									<span class="fa fa-download" />
								</h:commandLink>
							</p:column>
						</p:dataTable>
					</span>  
				</div>
			</div>                      
		</div>
	</div>
</ui:composition>
