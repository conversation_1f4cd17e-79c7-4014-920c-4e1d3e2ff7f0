<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:sec="http://www.springframework.org/security/tags"
	xmlns:jsf="http://xmlns.jcp.org/jsf">

	<div class="row">
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default required">
				<label>Descrição</label>
				<input jsf:id="descricao" type="text" maxlength="255" jsf:value="#{templateRemessaBean.entity.descricao}"
					required="true" jsf:required="true" jsf:label="Descrição" class="form-control"
					disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Tipo do Template</label>
				<select jsf:id="tipoTemplate" jsf:value="#{templateRemessaBean.entity.tipoTemplateRemessa}" disabled="#{disabled}"
					class="form-control full-width select2" size="1" required="required">
					<f:selectItem itemLabel="Selecione" />
					<f:ajax execute="@this" render="complemento" onevent="function(data) { $.masks(); }"/>
					<f:selectItems value="#{templateRemessaBean.tiposTemplate}" var="i" itemValue="#{i}" itemLabel="#{i.descricao}" />
				</select>
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Ativo</label>
				<select jsf:id="ativo" jsf:value="#{templateRemessaBean.entity.ativo}"
					class="form-control full-width select2" required="true" jsf:required="true" size="1"
					disabled="#{disabled}" jsf:label="Ativo">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
				</select>
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Integra+</label>
				<select jsf:id="integra" jsf:value="#{templateRemessaBean.entity.integraMais}"
					class="form-control full-width select2" required="true" jsf:required="true" size="1"
					disabled="#{disabled}" jsf:label="Ativo">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
				</select>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default required">
				<label>Iniciar na Linha</label>
				<input jsf:id="linhaInicial" type="text" maxlength="2"
					jsf:value="#{templateRemessaBean.entity.linhaInicial}" jsf:label="Linha Inicial"
					class="form-control integer" disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default required">
				<label>Ignorar Últ. Linhas</label>
				<input jsf:id="linhaFinal" type="text" maxlength="2"
					jsf:value="#{templateRemessaBean.entity.linhaFinal}" jsf:label="Linha Final"
					class="form-control integer" disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default">
				<label>Posição Inicial</label>
				<input jsf:id="posicaoInicial" type="text" maxlength="2"
					jsf:value="#{templateRemessaBean.entity.posicaoInicial}" jsf:label="Posição Inicial"
					class="form-control integer" disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default form-group-default-select2">
				<label>UF</label>
				<select jsf:id="uf" jsf:value="#{templateRemessaBean.entity.uf}" jsf:label="UF"
						class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperSessionBean.ufsCredenciamento}" var="e" itemValue="#{e}" itemLabel="#{e}" />
				</select>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-xl-3 col-lg-5">
			<div class="form-group form-group form-group-default">
				<label>Template de Retorno</label>
				<select jsf:id="templateRemessaRetorno"
						jsf:value="#{templateRemessaBean.entity.templateRetorno}"
						class="form-control full-width select2" size="1" jsf:label="Template de Retorno" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{templateRemessaBean.templates}" var="i"
								   itemValue="#{i.id}" itemLabel="#{i.descricao}" />
				</select>
			</div>
		</div>
		<div class="col-xl-7 col-lg-7">
			<div class="form-group form-group form-group-default form-group-default-select2">
				<label>Financeira</label>
				<select jsf:id="financeira" jsf:value="#{templateRemessaBean.entity.financeira}" class="form-control full-width select2" jsf:label="Financeira"
					disabled="#{disabled}">
					<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
					<f:converter converterId="financeiraConverter"/>
				</select>
			</div>
		</div>
		<div class="col-xl-2 col-lg-5">
			<div class="form-group form-group-default form-group-default-select2">
				<label>Enviar Email</label>
				<select jsf:id="enviarEmail" jsf:value="#{templateRemessaBean.entity.enviarEmail}" jsf:label="Enviar Email"
						class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione "/>
					<f:selectItems value="#{helperBean.simNao}" var="e"  itemLabel="#{e.descricao}"/>
				</select>
			</div>
		</div>
	</div>
	<div class="row" jsf:id="complemento">
		<div class="col-lg-2">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Ordem</h6>
			</div>
		</div>
		<div class="col-lg-#{templateRemessaBean.entity.posicional ? '3' : '4'}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Campo</h6>
			</div>
		</div>
		<div class="col-lg-#{templateRemessaBean.entity.posicional ? '3' : '4'}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Tipo</h6>
			</div>
		</div>
		<div class="col-lg-#{templateRemessaBean.entity.posicional and disabled eq 'disabled'? '1': '2'}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Obrig.</h6>
			</div>
		</div>
		<div class="col-lg-#{templateRemessaBean.entity.posicional and disabled eq 'disabled'? '1': '2'}" jsf:rendered="#{templateRemessaBean.entity.posicional}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Tamanho</h6>
			</div>
		</div>

		<div class="col-lg-1" jsf:rendered="#{templateRemessaBean.entity.posicional and disabled eq 'disabled'}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Início</h6>
			</div>
		</div>
		<div class="col-lg-1" jsf:rendered="#{templateRemessaBean.entity.posicional and disabled eq 'disabled'}">
			<div class="form-group form-group form-group-default">
				<h6 class="font-14 mt-4">Fim</h6>
			</div>
		</div>


		<ui:repeat prependId="false" var="c" value="#{templateRemessaBean.entity.campos}" varStatus="status">
			<div class="col-lg-2">
				<div class="form-group form-group form-group-default">
					#{c.ordem}
					<span class="mdi mdi-chevron-double-up" jsf:rendered="#{!status.first}">
						<f:ajax execute="@this" event="click" immediate="true"
							listener="#{templateRemessaBean.top(status.index)}"
							render="complemento :messages" onevent="function(data) { $.masks(); }"/>
					</span>
					<span class="mdi mdi-chevron-up" jsf:rendered="#{!status.first}">
						<f:ajax execute="@this" event="click" immediate="true"
							listener="#{templateRemessaBean.up(status.index)}"
							render="complemento :messages" onevent="function(data) { $.masks(); }"/>
					</span>
					<span class="mdi mdi-chevron-down" jsf:rendered="#{!status.last}">
						<f:ajax execute="@this" event="click" immediate="true"
							listener="#{templateRemessaBean.down(status.index)}"
							render="complemento :messages" onevent="function(data) { $.masks(); }"/>
					</span>
					<span class="mdi mdi-chevron-double-down" jsf:rendered="#{!status.last}">
						<f:ajax execute="@this" event="click" immediate="true"
							listener="#{templateRemessaBean.bottom(status.index)}"
							render="complemento :messages" onevent="function(data) { $.masks(); }"/>
					</span>
					<span class="mdi mdi-delete" jsf:rendered="#{!status.last}">
						<f:ajax execute="@this" event="click" immediate="true"
							listener="#{templateRemessaBean.remove(status.index)}"
							render="complemento :messages" onevent="function(data) { $.masks(); }"/>
					</span>
				</div>
			</div>
			<div class="col-lg-#{templateRemessaBean.entity.posicional ? '3' : '4'}">
				<div class="form-group form-group form-group-default">
					<label>#{c.campoFormularioRemessa.descricao}</label>
				</div>
			</div>
			<div class="col-lg-#{templateRemessaBean.entity.posicional ? '3' : '4'}">
				<div class="form-group form-group form-group-default form-group-default-select2">
					<select jsf:id="tipo" jsf:value="#{c.tipoCampoFormularioRemessa}" required="required" disabled="#{disabled}"
						class="form-control full-width select2" size="1">
						<f:selectItem itemLabel="Selecione" />
						<f:selectItems value="#{c.campoFormularioRemessa.tiposSuportados}" var="i" itemValue="#{i}" itemLabel="#{i.descricao}" />
					</select>
				</div>
			</div>
			<div class="col-lg-#{templateRemessaBean.entity.posicional and disabled eq 'disabled'? '1': '2'}">
				<div class="form-group form-group form-group-default form-group-default-select2">
					<select jsf:id="obrigatorio" jsf:value="#{c.obrigatorio}" required="required" disabled="#{disabled}"
						class="form-control full-width select2" size="1">
						<f:selectItem itemLabel="Sim" itemValue="#{true}"/>
						<f:selectItem itemLabel="Não" itemValue="#{false}"/>
					</select>
				</div>
			</div>
			<div jsf:rendered="#{templateRemessaBean.entity.posicional}" class="col-lg-#{templateRemessaBean.entity.posicional and disabled eq 'disabled'? '1': '2'}">
				<div class="form-group form-group form-group-default">
					<input jsf:id="tamanho" type="text" jsf:value="#{c.tamanho}" maxlength="3" required="required" disabled="#{disabled}"
						jsf:label="Tamanho" class="form-control integer"/>
				</div>
			</div>
			<div jsf:rendered="#{templateRemessaBean.entity.posicional and disabled eq 'disabled'}" class="col-lg-1">
				<div class="form-group form-group form-group-default">
					<input jsf:id="inicio" type="text" jsf:value="#{status.index == 0 ? templateRemessaBean.entity.posicaoInicial + 1 : templateRemessaBean.entity.posicaoAux + 1}" maxlength="4" required="required" disabled="#{disabled}"
						   jsf:label="Início" class="form-control integer"/>
				</div>
			</div>

			<div jsf:rendered="#{templateRemessaBean.entity.posicional and disabled eq 'disabled'}" class="col-lg-1">
				<div class="form-group form-group form-group-default">
					<input jsf:id="fim" type="text" jsf:value="#{status.index == 0 ? c.tamanho + templateRemessaBean.entity.posicaoInicial : templateRemessaBean.entity.posicaoAux + c.tamanho}" maxlength="4" required="required" disabled="#{disabled}"
						   jsf:label="Fim" class="form-control integer"/>
				</div>
			</div>

			#{status.index == 0 ? templateRemessaBean.entity.setPosicaoAux(c.tamanho + templateRemessaBean.entity.posicaoInicial) : templateRemessaBean.entity.setPosicaoAux(templateRemessaBean.entity.posicaoAux + c.tamanho)}

		</ui:repeat>
	</div>
</ui:composition>
