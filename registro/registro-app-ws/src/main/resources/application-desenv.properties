#datasource
spring.datasource.jndi-name=java:jboss/datasources/RegistroDS

#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.show-sql=true
spring.jpa.format-sql=true

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.mailgun.webhook-auth=**************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=desenv
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

#jsf
jsf.PROJECT_STAGE=Development

#diretorio para anexos de financeira
file-financeira.dir=/Users/<USER>/data/financeira/
file-financeira.dir-read=/Users/<USER>/data/financeira/

#diretorio para boletos
file-boleto.dir=/Users/<USER>/data/boletos/
file-boleto.dir-read=/Users/<USER>/data/

#diretorio para nf de cobranca
file-nf.dir=/Users/<USER>/data/notasFiscais/
file-nf.dir-read=/Users/<USER>/data/

# remessa
file.remessa.dir=C:/data/
file.remessa.dir-read=C:/data/

# remessa chassi
file.remessa.chassi.dir=C:/data/chassi/
file.remessa.chassi.dir-read=C:/data/chassi/

file.dir=c:/data/contratos/
file.dir-read=c:/data/contratos/

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

##certificado

detran.ce.cert.file.dir=C:\\storage\\certificados\\


#cripto
placecon.cripto.key.ecb=2BB80D537B1DA3E38BD30361AA855686

# MENSAGERIA - KAFKA
spring.kafka.producer.bootstrap-servers=localhost:29092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.client-id=group-id

spring.kafka.consumer.bootstrap-servers=localhost:29092
spring.kafka.consumer.group-id=group-id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

auto.create.topics.enable=true


#service DETRAN CE
detran.ce.default.uri=https://gravamews.detran.ce.gov.br/veiculows_dev
detran.ce.cliente.autenticado=/gravametw/api/autenticar/clienteAutenticado
detran.ce.registro.buscar.apontamento=/gravametw/contrato/api/apontamento
detran.ce.registro.buscar.apontamentopendente=/gravametw/contrato/api/apontamentoPendente
detran.ce.registro.buscar.apontamentospendentes=/gravametw/contrato/api/apontamentosPendentes
detran.ce.registro.buscar.contrato=/gravametw/contrato/api/contrato
detran.ce.registro.buscar.contratos=/gravametw/contrato/api/contratos
detran.ce.registro.buscar.contratosveiculo=/gravametw/contrato/api/contratosVeiculo
detran.ce.registro.buscar.municipiosce=/gravametw/contrato/api/municipios/ce
detran.ce.registro.cancelar=/gravametw/contrato/api/cancelar
detran.ce.registro.download.contratodigitalizado=/gravametw/contrato/api/downloadContratoDigitalizado
detran.ce.registro.enviar.contratodigitalizado=/gravametw/contrato/api/enviarContratoDigitalizado
detran.ce.registro.registrar=/gravametw/contrato/api/registrar
detran.ce.daes.buscar.dae=/daewstw/api/dae
detran.ce.daes.buscar.daes=/daewstw/api/daes
detran.ce.daes.gerar.dae=/daewstw/api/dae/gerar
detran.ce.daes.extrato.dae=/daewstw/api/dae/pdf
detran.ce.daes.buscar.servicos=/daewstw/api/servicos
detran.ce.daes.estoquedaes=/daewstw/api/dae/estoque

#service DETRAN AL

detran.al.default.uri=https://registros.detran.al.gov.br
detran.al.context.uri=/rdc-app-rest/rest/v2
detran.al.gerar.token=/auth
detran.al.consulta=/contrato/chassi/{chassi}
detran.al.registro=/contrato
detran.al.envio.imagem=/arquivo/chassi/{chassi}

#service DETRAN SE
detran.se.default.uri=https://api.registrocontrato.detran.se.gov.br
detran.se.cobranca=/api/v1/creditor/financial/generate
detran.se.gera.token=/api/v1/login
detran.se.refresh.token=/api/v1/refresh
detran.se.profile=/api/v1/profile
detran.se.criar.aditivo=/api/v1/creditor/additive
detran.se.consultar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.criar.registrar.contrato=/api/v1/creditor/contract/create/and/register
detran.se.criar.contrato=/api/v1/creditor/contract
detran.se.registrar.contrato=/api/v1/creditor/contract/{contract}/register
detran.se.atualizar.contrato=/api/v1/creditor/contract/{uuid}
detran.se.salvar.imagem=/api/v1/creditor/contract/save_image
detran.se.cancelar.contrato=/api/v1/creditor/contract/{contract}/canceled
detran.se.baixar.contrato=/api/v1/manager/contract/write-off
detran.se.cancelar.contrato.detran=/api/v1/manager/contract/cancel
detran.se.cnpjresponsible=06032507000103
detran.se.tokenvalidation=8a8836f6ffc560fd950f86f8e8d9052b33bc3337148c297ca8a3a265c7900e9c

# service DETRAN SP
detran.sp.default.uri=http://192.168.50.16:80/homol/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client
detran.sp.usuario=06032507000103
detran.sp.senha=PLACE@
# service DETRAN PR
detran.pr.default.uri.auth=https://auth-cs-hml.identidadedigital.pr.gov.br/centralautenticacao/api/v1/token
detran.pr.default.uri=https://homolog.registrodecontrato.detran.pr.gov.br/detran-regcon/api
detran.pr.usuario=22ac3c5a5bf0b520d281c122d1490650
detran.pr.senha=pl4c3t1
# service DETRAN AP
detran.ap.default.uri.auth=
detran.ap.default.uri=
detran.ap.url.cadastrar=/api/areaRestrita/registroContrato/registrar
detran.ap.url.consultar=/api/areaRestrita/registroContrato/consultar
detran.ap.usuario=
detran.ap.senha=
# service DETRAN RJ
detran.rj.default.uri=http://*************:8080/wsstack/services/REG-CONT
detran.rj.context.path=com.registrocontrato.registro.service.detran.rj.client
detran.rj.usuario=COCTPTIN
detran.rj.senha=SENHA01
# service DETRAN PI
detran.pi.default.uri-boleto=http://*************:8080/ws-detran/rest/detran/registro/ws/gerarBoleto
detran.pi.default.uri=http://localhost:8080/ws-detran/registroContratoWS
detran.pi.context.path=com.registrocontrato.registro.service.detran.pi.client
detran.pi.usuario=06032507000103
detran.pi.senha=p@c3TecIn0V
# service DETRAN RR
detran.rr.default.uri=https://loiwh3.searchtecnologia.com.br
detran.rr.url.cadastrar=/getranServicos/rest/registroContrato/registrarContrato
detran.rr.url.consultarcontrato=/getranServicos/rest/registroContrato/consultarContrato
detran.rr.usuario=PLACETI
detran.rr.senha=ksokv_58azisplgjjazzmb.
# service DETRAN MG
detran.mg.default.uri=http://webservicehomologa.detran.mg.gov.br/sircof/soap/contratos_financeiros/service
detran.mg.context.path=com.registrocontrato.registro.service.detran.mg.client
detran.mg.usuario=06032507000103
detran.mg.senha=9f87fc5fea1f0c56cfa4c54844221c8923aec3ef
# service DETRAN SC
detran.sc.default.uri=
detran.sc.context.path=com.registrocontrato.registro.service.detran.sc.client
detran.sc.usuario=
detran.sc.senha=
detran.sc.registrarcontrato=RegistrarContrato
detran.sc.consultarsequencial=ConsultarSequencialContrato
# service DETRAN BA
detran.ba.default.uri=http://200.187.13.80/wsdetrancontrato/wsdetrancontrato.asmx
detran.ba.context.path=com.registrocontrato.registro.service.detran.ba.client
detran.ba.usuario=100403
detran.ba.senha=PlaceT
# service DETRAN PB
detran.pb.default.uri=https://wsdetran.pb.gov.br/CallWS-Desenvolvimento/CallWSDT
detran.pb.context.path=com.registrocontrato.registro.service.detran.pb.client
detran.pb.usuario=0603250
detran.pb.senha=DET123
detran.pb.codigo=000955
detran.pb.sigla.transacao=PL
detran.pb.sigla.transmissao=PL
# service DETRAN AC
detran.ac.default.uri=https://loiwh3.searchtecnologia.com.br/ac/getranServicos/rest/registroContrato/
detran.ac.context.path=com.registrocontrato.registro.service.detran.ac.client
detran.ac.url.cadastrar=registrarContrato
detran.ac.url.consultar=consultarContrato
detran.ac.usuario=PLACETI
detran.ac.senha=ksokv_58azisplgjjazzmb.
# service DETRAN MT
detran.mt.default.uri=http://wbs2.homologa.detrannet.mt.gov.br/wsRegistroContrato/wsRegistroContrato.asmx
detran.mt.default.endpoint=http://wbs2.homologa.detrannet.mt.gov.br
detran.mt.context.path=com.registrocontrato.registro.service.detran.mt.client
detran.mt.url.cadastrar=RegistraContrato
detran.mt.url.consultar=ConsultaContrato
# service DETRAN PE
detran.pe.default.uri=http://200.238.67.2:41075/WebApiRegistroContratoGravame/RegistraContrato
detran.pe.default.cobranca.uri=http://200.238.67.2:42675/Estabelecimento/Obter/MovimentacaoFinanceira
# service DETRAN RS
detran.rs.default.uri=https://www.vei.detran.rs.gov.br/sng/ContratoIncSoap
detran.rs.context.path=com.registrocontrato.registro.service.detran.rs.client

# service DETRAN MS
detran.ms.default.uri=https://web2.detran.ms.gov.br/s85/rc-api
detran.ms.url.cadastrar=/solicitacao/solicitar-registro
detran.ms.url.autenticacao=/usuario/authenticate
detran.ms.url.desbloquear.contrato=/solicitacao/desbloquear-contrato
detran.ms.url.envio.imagem=/solicitacao/enviar-imagem
detran.ms.url.corrigir.imagem=/solicitacao/corrigir-imagem
detran.ms.url.busca.andamento=/buscar/andamento
detran.ms.url.consulta.boleto=/buscar/guia-pagamento
detran.ms.url.consulta.boleto.byte=/buscar/guia-pagamento/byte

# service DETRAN RN
detran.rn.default.uri=https://sudapih.detran.rn.gov.br
detran.rn.usuario=06032507000103
detran.rn.senha=Pl@c&T1D&tr@n_Pl@c&c0N
detran.rn.url.autenticacao=/auth/login
detran.rn.url.comunicarcontrato=/convenio/externo/registrocontrato/comunicarcontratofinanciamentoveiculo
detran.rn.url.consulta.debito=/convenio/externo/registrocontrato/listardebitoagentefinanceiro
detran.rn.url.consulta.debito.detalhado=/convenio/externo/registrocontrato/listardebitodetalhado
detran.rn.url.regerar.debito=/convenio/externo/registrocontrato/regerardebito

# service ARQDIGITAL

arqdigital.baseurl=https://gecov.com.br/ws
arqdigital.autenticacao=/login
arqdigital.consulta.tipo.documento=/tiposDocumento
arqdigital.registro=/servicos/protocolarRegistro
arqdigital.envio.arquivo=/binario/enviarArquivo/{idDocumento}
arqdigital.pesquisa.documento=/servicos/pesquisaDocumento
arqdigital.quitacao.contrato=/servicos/solicitarQuitacao
arqdigital.definicao.perfil=/login/definirPerfil
